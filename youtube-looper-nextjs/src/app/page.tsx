'use client'

import { MainLayout } from '@/components/layout/MainLayout'
import { VideoPlayer } from '@/components/video-player/VideoPlayer'
import { TimeframeDisplay } from '@/components/video-player/TimeframeDisplay'
import { CurrentQueue } from '@/components/queue/CurrentQueue'
import { SearchView } from '@/components/search/SearchView'
import { PersonalQueuesView } from '@/components/personal-queues/PersonalQueuesView'
import { PublicQueuesView } from '@/components/public-queues/PublicQueuesView'
import { useNavigation } from '@/hooks/useNavigation'
import { useHeaderMinimize } from '@/components/layout/Header'
import { useQueue } from '@/hooks/useQueue'
import { firebaseService } from '@/lib/services/firebase'
import { useFirebase } from '@/components/providers/FirebaseProvider'
import { transformQueueData } from '@/lib/utils/queue-transform'
import { useEffect, useState } from 'react'

export default function HomePage() {
  const { activeView } = useNavigation()
  const { isMinimized } = useHeaderMinimize()
  const { loadQueue } = useQueue()
  const { isInitialized, db } = useFirebase()
  const [isLoadingSharedQueue, setIsLoadingSharedQueue] = useState(false)

  useEffect(() => {
    // Initialize YouTube API when component mounts
    const initYouTubeAPI = () => {
      if (typeof window !== 'undefined' && !window.YT) {
        const tag = document.createElement('script')
        tag.src = 'https://www.youtube.com/iframe_api'
        const firstScriptTag = document.getElementsByTagName('script')[0]
        firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)
      }
    }

    initYouTubeAPI()
  }, [])

  // Separate effect for checking URL queue that waits for Firebase initialization
  useEffect(() => {
    // Only proceed if Firebase is initialized
    if (!isInitialized) {
      return
    }

    // Check for queue ID in URL parameters
    const checkURLForQueue = async () => {
      const urlParams = new URLSearchParams(window.location.search)
      const queueId = urlParams.get('q')

      if (queueId) {
        console.log('🔗 Found queue ID in URL:', queueId)

        // Check if Firebase database is available
        if (!db) {
          console.warn('⚠️ Firebase not configured, cannot load shared queue')
          return
        }

        setIsLoadingSharedQueue(true)

        try {
          const publicQueue = await firebaseService.getPublicQueue(queueId)
          if (publicQueue) {
            // Transform queue data with auto-play enabled for shared queues
            const transformedQueueData = transformQueueData(publicQueue.queueData, true)
            loadQueue(transformedQueueData)
            console.log('✅ Queue loaded from URL:', publicQueue.metadata.title)

            // Clean up URL without refreshing
            window.history.replaceState({}, document.title, window.location.pathname)
          } else {
            console.warn('Queue not found:', queueId)
          }
        } catch (error) {
          console.error('Failed to load queue from URL:', error)
        } finally {
          setIsLoadingSharedQueue(false)
        }
      }
    }

    checkURLForQueue()
  }, [isInitialized, db, loadQueue])

  const renderActiveView = () => {
    switch (activeView) {
      case 'search':
        return <SearchView />
      case 'personal':
        return <PersonalQueuesView />
      case 'public':
        return <PublicQueuesView />
      default:
        return <SearchView />
    }
  }

  return (
    <MainLayout>
      {/* Loading overlay for shared queue - positioned to not affect layout */}
      {isLoadingSharedQueue && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
          style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
        >
          <div className="bg-dark-800 rounded-lg p-6 flex items-center space-x-3 shadow-2xl">
            <div className="w-6 h-6 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
            <span className="text-white font-medium">Loading shared queue...</span>
          </div>
        </div>
      )}

      {/* Media Control Center - Video Player and Current Queue */}
      <div
        className={`media-control-center ${
          isLoadingSharedQueue
            ? '' // Disable transitions during loading to prevent layout shifts
            : 'transition-all duration-500 ease-in-out'
        } ${
          isMinimized
            ? 'opacity-0 max-h-0 mb-0 overflow-hidden pointer-events-none transform -translate-y-4'
            : 'opacity-100 mb-8 transform translate-y-0'
        }`}
      >
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <VideoPlayer />
          <CurrentQueue />
        </div>

        {/* Timeframe Display - Full Width Below Player and Queue */}
        <TimeframeDisplay />
      </div>

      {/* Main Content Views */}
      <div className="content-area">
        {renderActiveView()}
      </div>
    </MainLayout>
  )
}
