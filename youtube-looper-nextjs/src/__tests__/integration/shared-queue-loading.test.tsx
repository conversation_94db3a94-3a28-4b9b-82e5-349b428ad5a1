/**
 * Unit test for shared queue loading functionality
 * Tests the fix for the bug where shared queues don't load in different browsers
 */

import { firebaseService } from '@/lib/services/firebase'

// Mock Firebase service
jest.mock('@/lib/services/firebase', () => ({
  firebaseService: {
    getPublicQueue: jest.fn(),
  },
}))

// Mock Firebase config
jest.mock('@/lib/firebase/config', () => ({
  initializeFirebase: jest.fn(() => ({
    app: { mockApp: true },
    db: { mockDb: true },
    auth: { mockAuth: true },
  })),
  getFirebaseDb: jest.fn(() => ({ mockDb: true })),
  getFirebaseAuth: jest.fn(() => ({ mockAuth: true })),
  getFirebaseApp: jest.fn(() => ({ mockApp: true })),
}))

describe('Shared Queue Loading', () => {
  const mockFirebaseService = firebaseService as jest.Mocked<typeof firebaseService>

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should call getPublicQueue when Firebase is initialized and queue ID is present', async () => {
    // Mock a successful queue fetch
    const mockQueue = {
      id: 'test-queue-id',
      queueData: {
        items: [],
        currentIndex: 0,
        isPlaying: false,
        queueLoopCount: -1,
        shuffle: false,
        volume: 1,
        timestamp: Date.now(),
      },
      metadata: {
        title: 'Test Shared Queue',
        isPublic: true,
      },
      userId: 'test-user',
      isPublic: true,
      createdAt: Date.now(),
      lastModified: Date.now(),
    }

    mockFirebaseService.getPublicQueue.mockResolvedValue(mockQueue)

    // Test the Firebase service directly
    const result = await firebaseService.getPublicQueue('test-queue-id')

    expect(mockFirebaseService.getPublicQueue).toHaveBeenCalledWith('test-queue-id')
    expect(result).toEqual(mockQueue)
  })

  it('should handle queue not found gracefully', async () => {
    // Mock queue not found
    mockFirebaseService.getPublicQueue.mockResolvedValue(null)

    const result = await firebaseService.getPublicQueue('nonexistent-queue-id')

    expect(mockFirebaseService.getPublicQueue).toHaveBeenCalledWith('nonexistent-queue-id')
    expect(result).toBeNull()
  })

  it('should handle Firebase service errors', async () => {
    // Mock Firebase service error
    const error = new Error('Firebase connection failed')
    mockFirebaseService.getPublicQueue.mockRejectedValue(error)

    try {
      await firebaseService.getPublicQueue('test-queue-id')
    } catch (e) {
      expect(e).toBe(error)
    }

    expect(mockFirebaseService.getPublicQueue).toHaveBeenCalledWith('test-queue-id')
  })
})
